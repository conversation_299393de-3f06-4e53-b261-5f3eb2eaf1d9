<script>
    import { H2, P1, P2, But<PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    export let sectionLabel = "{Your Product} Demo";
    export let headline = "See {your product} in action";
    export let subheadline = "Click play to with this {time amount} demo";
    export let primaryButton = "Get Started";
    export let secondaryButton = "Button";
    export let videoPlaceholder = "VIDEO DEMO";
</script>

<SectionWrapper --bg-color="var(--light-yellow)" --padding-top="8rem" --padding-bottom="8rem">
<div class="demo-section">
    <div class="demo-content">
        <P2>{@html sectionLabel}</P2>
        <H2>{@html headline}</H2>
        <P1>{@html subheadline}</P1>

        <div class="demo-buttons">
            <Button>{primaryButton}</Button>
            <Button isSecondary>{secondaryButton}</Button>
        </div>
    </div>

    <div class="demo-video">
        <div class="video-placeholder">
            <div class="play-button">▶</div>
            <P2>{videoPlaceholder}</P2>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* Demo Section */
    .demo-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        max-width: 1200px;
        width: 100%;
    }
    
    .demo-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }
    
    .demo-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .demo-video {
        display: flex;
        justify-content: center;
    }
    
    .video-placeholder {
        width: 100%;
        height: 300px;
        background: var(--pitch-black);
        color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border: 4px solid var(--pitch-black);
        font-weight: 700;
        gap: 1rem;
        position: relative;
        cursor: pointer;
    }
    
    .play-button {
        background: var(--yellow);
        color: var(--pitch-black);
        width: 4rem;
        height: 4rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        border: 3px solid var(--pitch-black);
    }
    
    .video-placeholder:hover .play-button {
        transform: scale(1.1);
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .demo-section {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .video-placeholder {
            height: 200px;
        }
    }
</style>
