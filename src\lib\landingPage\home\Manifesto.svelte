<script>
    import { H2, P1, P2, <PERSON><PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    export let sectionLabel = "Manifesto";
    export let headline = "We want you to {ability they couldn't do before}";
    export let founderName = "{your name}";
    export let companyName = "{your company}";
    export let introText = "Write a few sentences introducing the problems you solve. You can even ask a rhetorical question to get people thinking about their own personal experiences with a problem they might have encountered.";
    export let backstoryText = "This is your chance to tell the backstory of why your product exists and explain the gap your product fills. Don't be afraid to take a stance and plant your flag.";
    export let beliefStatement = "{you should be able to X without Y}";
    export let helpItems = [
        "Solve this problem",
        "Solve another problem",
        "Solve this other problem"
    ];
    export let primaryButton = "Sign Up";
    export let secondaryButton = "More about us";
    export let closingText = "If that resonates with you, you're in the right place.";
    export let signature = "- (your name)";
    export let imagePlaceholder = "FOUNDER IMAGE";
</script>

<SectionWrapper --bg-color="var(--light-purple)" --padding-top="8rem" --padding-bottom="8rem">
<div class="manifesto-section">
    <div class="manifesto-content">
        <P2>{sectionLabel}</P2>
        <H2>{@html headline}</H2>
        
        <div class="manifesto-text">
            <P1><strong>Hey, I'm {@html founderName}, the founder of {@html companyName}.</strong></P1>
            <P1>{introText}</P1>
            <P1>{backstoryText}</P1>
            <P1>We believe that {@html beliefStatement}. We'll help you:</P1>
            
            <div class="manifesto-list">
                {#each helpItems as item}
                    <div class="list-item">
                        <div class="arrow-icon">→</div>
                        <P1>{item}</P1>
                    </div>
                {/each}
            </div>

            <P1>{closingText}</P1>
            <P1><strong>{signature}</strong></P1>
        </div>

        <div class="manifesto-buttons">
            <Button>{primaryButton}</Button>
            <Button isSecondary>{secondaryButton}</Button>
        </div>
    </div>

    <div class="manifesto-image">
        <div class="placeholder-image">
            <P2>{imagePlaceholder}</P2>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* Manifesto Section */
    .manifesto-section {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 4rem;
        align-items: center;
        max-width: 1200px;
        width: 100%;
    }
    
    .manifesto-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }
    
    .manifesto-text {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
    }
    
    .manifesto-list {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        margin: 1rem 0;
    }
    
    .list-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .arrow-icon {
        background: var(--pitch-black);
        color: white;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        flex-shrink: 0;
    }
    
    .manifesto-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .manifesto-image {
        display: flex;
        justify-content: center;
    }
    
    .placeholder-image {
        width: 100%;
        height: 400px;
        background: var(--pitch-black);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 4px solid var(--pitch-black);
        font-weight: 700;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .manifesto-section {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .placeholder-image {
            height: 250px;
        }
    }
</style>
