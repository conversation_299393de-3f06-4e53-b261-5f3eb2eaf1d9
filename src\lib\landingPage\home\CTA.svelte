<script>
    import { H2, P1, P2, But<PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    export let headline = "Switch to {your product} in {time amount} or less";
    export let subheadline = "You shouldn't have to {experience a painful problem}. With {your product}, you can finally {achieve a value outcome or get a big improvement}.";
    export let features = [
        "No credit card required",
        "Cancel any time",
        "Live chat support"
    ];
    export let primaryCTA = "{Primary CTA}";
    export let secondaryCTA = "{Secondary CTA}";
    export let socialProof = "{Social Proof goes here}";
    export let imagePlaceholder = "CTA IMAGE";
</script>

<SectionWrapper --bg-color="var(--rose)" --padding-top="8rem" --padding-bottom="8rem">
<div class="cta-section">
    <div class="cta-content">
        <H2>{@html headline}</H2>
        <P1>{@html subheadline}</P1>

        <div class="cta-features">
            {#each features as feature}
                <div class="feature-item">
                    <div class="check-icon">✓</div>
                    <P2>{feature}</P2>
                </div>
            {/each}
        </div>

        <div class="cta-buttons">
            <Button>{@html primaryCTA}</Button>
            <Button isSecondary>{@html secondaryCTA}</Button>
        </div>
        
        <div class="social-proof-item">
            <div class="check-icon">✓</div>
            <P2>{@html socialProof}</P2>
        </div>
    </div>
    
    <div class="cta-image">
        <div class="placeholder-image">
            <P2>{imagePlaceholder}</P2>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* CTA Section */
    .cta-section {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        max-width: 1200px;
        width: 100%;
    }
    
    .cta-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
    }
    
    .cta-features {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .feature-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .check-icon {
        background: var(--pitch-black);
        color: white;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        flex-shrink: 0;
    }
    
    .cta-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .social-proof-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 3px solid var(--pitch-black);
        background: white;
    }
    
    .cta-image {
        display: flex;
        justify-content: center;
    }
    
    .placeholder-image {
        width: 100%;
        height: 400px;
        background: var(--pitch-black);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 4px solid var(--pitch-black);
        font-weight: 700;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .cta-section {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .placeholder-image {
            height: 250px;
        }
    }
</style>
