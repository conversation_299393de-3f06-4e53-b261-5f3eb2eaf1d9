<script>
    import { H3 } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    export let headline = "Don't just take our word for it";
    export let logoCount = 10;
</script>

<SectionWrapper --bg-color="var(--pitch-black)" --padding-top="4rem" --padding-bottom="4rem">
<div class="social-proof">
    <H3 --text-color="white">{headline}</H3>
    <div class="logos-grid">
        {#each Array(logoCount) as _, i}
            <div class="logo-item">
                <div class="logo-placeholder">LOGO</div>
            </div>
        {/each}
    </div>
</div>
</SectionWrapper>

<style>
    /* Social Proof */
    .social-proof {
        text-align: center;
        max-width: 1200px;
        width: 100%;
    }
    
    .logos-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 2rem;
        margin-top: 3rem;
    }

    .logo-item {
        display: flex;
        justify-content: center;
    }

    .logo-placeholder {
        background: white;
        color: var(--pitch-black);
        padding: 2rem 1rem;
        border: 4px solid var(--pitch-black);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        min-height: 80px;
        width: 100%;
    }

    .logo-placeholder:hover {
        transform: translate(-4px, -4px);
        box-shadow: 8px 8px 0 var(--pitch-black);
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .logos-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .logo-placeholder {
            padding: 1.5rem 0.5rem;
            min-height: 60px;
        }
    }
</style>
