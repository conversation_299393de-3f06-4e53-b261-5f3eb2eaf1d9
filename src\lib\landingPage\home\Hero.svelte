<script>
    import { H1, P1, P2, But<PERSON> } from '$lib/ui';
    import { SectionWrapper } from '$lib/landingPage';
    
    export let headline = "You don't need to burn out to get a high SAT score";
    export let subheadline = "The only SAT platform for high-scoring students that help you beat the test without burning out.";
    export let features = [
        "Know exactly what to study to increase your score",
        "Surround yourself with a community of motivated students", 
        "Get you addicted to learning"
    ];
    export let primaryCTA = "Get Addicted";
    export let secondaryCTA = "Explore More";
    export let riskReversal = "Money back guarantee <b>(it's free)</b>";
</script>

<SectionWrapper --bg-color="var(--rose)" --padding-top="8rem" --padding-bottom="8rem">
<div class="hero-container">
    <div class="hero-content">
        <H1 --text-align="center">{@html headline}</H1>
        <P1>{@html subheadline}</P1>
        
        <div class="hero-features">
            {#each features as feature}
                <div class="feature-item">
                    <div class="check-icon">✓</div>
                    <P2>{feature}</P2>
                </div>
            {/each}
        </div>
        
        <div class="hero-buttons">
            <Button>{@html primaryCTA}</Button>
            <Button isSecondary>{@html secondaryCTA}</Button>
        </div>

        <div class="risk-reversal">
            <div class="check-icon">✓</div>
            <P2>{@html riskReversal}</P2>
        </div>
    </div>
    
    <div class="hero-image">
        <div class="placeholder-image">
            <P2>HERO IMAGE</P2>
        </div>
    </div>
</div>
</SectionWrapper>

<style>
    /* Hero */
    .hero-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        max-width: 1200px;
        width: 100%;
    }
    
    .hero-content {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        text-align: left;
    }
    
    .hero-features {
        display: flex;
        flex-direction: column;
        gap: 1rem;
    }
    
    .feature-item {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .check-icon {
        background: var(--pitch-black);
        color: white;
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
    }
    
    .hero-buttons {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }
    
    .risk-reversal {
        display: flex;
        align-items: center;
        gap: 1rem;
        /* justify-content: center; */
        opacity: 0.8;
    }
    
    .hero-image {
        display: flex;
        justify-content: center;
    }
    
    .placeholder-image {
        width: 100%;
        height: 400px;
        background: var(--pitch-black);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 4px solid var(--pitch-black);
        font-weight: 700;
    }
    
    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .hero-container {
            grid-template-columns: 1fr;
            gap: 2rem;
        }
        
        .hero-content {
            text-align: center;
        }
        
        .placeholder-image {
            height: 250px;
        }
    }
</style>
